CREATE TABLE clients
(
    id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name          TEXT NOT NULL,
    email         TEXT NOT NULL,
    rep_full_name TEXT NOT NULL,
    created_at    TIMESTAMPTZ      DEFAULT current_timestamp
);

CREATE TABLE billing_information
(
    id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_id    UUID REFERENCES clients (id) ON DELETE CASCADE,
    company_name TEXT,
    vat_number   TEXT,
    eik          TEXT,
    mol          TEXT,
    address      TEXT,
    country      TEXT,
    city         TEXT,
    is_bulgarian BOOLEAN          DEFAULT FALSE,
    created_at   TIMESTAMPTZ      DEFAULT current_timestamp
);

ALTER TABLE proposals
DROP
COLUMN IF EXISTS client_name,
  DROP
COLUMN IF EXISTS client_email,
  DROP
COLUMN IF EXISTS client_rep_full_name;

ALTER TABLE proposals
    ADD COLUMN client_id UUID REFERENCES clients (id) ON DELETE SET NULL,
  ADD COLUMN summary TEXT[];
