import express, { Router } from 'express';
import proposalsRouter from './routes/proposals';
import usersRouter from './routes/users';

const apiRouter = Router();

apiRouter.use(
  '/proposals/payments/stripe/webhook',
  express.raw({ type: 'application/json' }),
);
apiRouter.use('/proposals', express.json(), proposalsRouter);
apiRouter.use('/users', express.json(), usersRouter);

export default apiRouter;
