import express, { Router } from 'express';
import {
  ProposalCreateRequestSchema,
  ProposalEditRequest,
  ProposalEditRequestSchema,
} from '../../lib/validators';
import { getStripe, getSupabaseClient, validateRequest } from '../services';
import { Proposal } from '../../lib/models';
import { requireAuth } from '../middlewares';

const router = Router();

router.post('/', requireAuth, async (req, res) => {
  const validation = validateRequest(ProposalCreateRequestSchema, req.body, {
    headers: req.headers,
  });

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const proposalRequest: any = {};

  const proposal: Proposal = {
    ...proposalRequest,
    proposal_id: buildProposalId(),
    status: 'draft',
  };

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .insert([proposal])
    .select()
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  return res.json(data);
});

router.patch('/:id', async (req, res) => {
  const validation = validateRequest(ProposalEditRequestSchema, req.body, {
    headers: req.headers,
  });

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { id } = req.params;

  const proposalRequest: ProposalEditRequest = validation.data;

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .update(proposalRequest)
    .eq('id', id);

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  return res.json(data);
});

router.get('/:id', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  const { data, error } = await getSupabaseClient()
    .from('proposals')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal server error' });
  }

  if (!data) {
    return res.status(404).json({ error: 'Proposal not found' });
  }

  return res.json(data);
});

router.post('/:id/payments', async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return res.status(400).json({ error: 'Missing proposal ID' });
  }

  const { data: proposal, error } = await getSupabaseClient()
    .from('proposals')
    .select('*')
    .eq('id', id)
    .single();

  if (error || !proposal) {
    console.error(error);
    return res.status(404).json({ error: 'Proposal not found' });
  }

  if (proposal.status === 'paid') {
    return res.status(400).json({ error: 'Proposal is already fully paid' });
  }

  const isInitialPayment =
    proposal.status === 'draft' || proposal.status === 'sent';
  const amount = proposal.paid_amount
    ? proposal.price - proposal.paid_amount
    : proposal.price * 0.5;

  try {
    const session = await getStripe().checkout.sessions.create({
      mode: 'payment',
      line_items: [
        {
          price_data: {
            currency: proposal.currency.toLowerCase(),
            unit_amount: Math.round(amount * 100),
            product_data: {
              name: `Project ${proposal.proposal_id} - ${isInitialPayment ? 'Initial Payment' : 'Final Payment'}`,
              description: proposal.project_title,
            },
          },
          quantity: 1,
        },
      ],
      billing_address_collection: 'required',
      customer_creation: 'always',
      metadata: {
        proposal_id: proposal.id,
      },
      success_url: `${process.env['BASE_URL']}/successful-payment?p=${proposal.id}`,
      cancel_url: `${process.env['BASE_URL']}/p/${proposal.id}`,
    });

    if (isInitialPayment) {
      await getSupabaseClient()
        .from('proposals')
        .update({
          accepted_at: new Date().toISOString(),
          accepted_ip: req.ip,
        })
        .eq('id', proposal.id);
    }

    return res.json({ url: session.url });
  } catch (err: any) {
    console.error('Stripe error:', err);
    return res.status(500).json({ error: 'Stripe session creation failed' });
  }
});

router.post(
  '/payments/stripe/webhook',
  express.raw({ type: 'application/json' }),
  async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env['STRIPE_WEBHOOK_SECRET'];

    let event;

    try {
      event = getStripe().webhooks.constructEvent(
        req.body,
        sig as string,
        endpointSecret!,
      );
    } catch (err: any) {
      console.error(
        '❌ Stripe webhook signature verification failed.',
        err.message,
      );

      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    if (event.type === 'checkout.session.completed') {
      const session = event.data.object;
      const proposalId = session.metadata!['proposal_id'];

      if (proposalId) {
        const { data: proposal } = await getSupabaseClient()
          .from('proposals')
          .select('*')
          .eq('id', proposalId)
          .single();

        if (proposal) {
          const newPaidAmount =
            (proposal.paid_amount || 0) + session.amount_total! / 100;
          const status =
            newPaidAmount >= proposal.price ? 'paid' : 'partially_paid';

          await getSupabaseClient()
            .from('proposals')
            .update({ paid_amount: newPaidAmount, status })
            .eq('id', proposalId);
        }
      }
    }

    return res.status(200).json({ received: true });
  },
);

function buildProposalId() {
  return (
    'CHAIN-' +
    new Date().toISOString().slice(0, 10).replaceAll('-', '') +
    '-' +
    Math.floor(Math.random() * 1000000)
  );
}

export default router;
