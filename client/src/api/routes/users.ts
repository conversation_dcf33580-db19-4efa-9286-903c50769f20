import { Router } from 'express';
import { getSupabaseClient, validateRequest } from '../services';
import { SignInRequestSchema } from '../../lib/validators';

function setAuthCookies(
  res: any,
  session: { access_token: string; refresh_token: string; expires_in: number },
) {
  res.cookie('access_token', session.access_token, {
    httpOnly: true,
    secure: true,
    sameSite: 'strict',
    maxAge: session.expires_in * 1000,
  });

  res.cookie('refresh_token', session.refresh_token, {
    httpOnly: true,
    secure: true,
    sameSite: 'strict',
    maxAge: 7 * 24 * 60 * 60 * 1000,
  });
}

const router = Router();

router.post('/sign-in', async (req, res) => {
  const validation = validateRequest(SignInRequestSchema, req.body, {
    headers: { ...req.headers },
  });

  if (!validation.success) {
    return res.status(400).json({
      error: validation.error,
      details: validation.details,
    });
  }

  const { email, password } = validation.data;

  const { data, error } = await getSupabaseClient().auth.signInWithPassword({
    email,
    password,
  });

  if (error || !data.session) {
    console.error('Sign-in failed:', error?.message);
    return res
      .status(401)
      .json({ error: error?.message || 'Invalid credentials' });
  }

  setAuthCookies(res, data.session);
  return res.json(data.session);
});

router.post('/refresh', async (req, res) => {
  const refreshToken = req.cookies?.refresh_token;
  if (!refreshToken) {
    return res.status(400).json({ error: 'Missing refresh token' });
  }

  const { data, error } = await getSupabaseClient().auth.refreshSession({
    refresh_token: refreshToken,
  });

  if (error || !data.session) {
    return res
      .status(401)
      .json({ error: error?.message || 'Unable to refresh session' });
  }

  setAuthCookies(res, data.session);
  return res.json(data.session);
});

router.get('/me', async (req, res) => {
  const accessToken = req.cookies?.access_token;
  if (!accessToken) {
    return res.status(401).json({ error: 'Missing access token' });
  }

  const { data, error } = await getSupabaseClient().auth.getUser(accessToken);

  if (error || !data.user) {
    return res
      .status(401)
      .json({ error: error?.message || 'Invalid or expired token' });
  }

  return res.json({ user: data.user });
});

export default router;
