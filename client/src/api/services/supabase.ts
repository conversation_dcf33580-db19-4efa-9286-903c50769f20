import { createClient, SupabaseClient } from '@supabase/supabase-js';

let supabase: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient {
  if (!supabase) {
    const url = process.env['SUPABASE_URL'];
    const key = process.env['SUPABASE_ANON_KEY'];

    if (!url || !key) {
      throw new Error('Missing Supabase env variables');
    }

    supabase = createClient(url, key);
  }

  return supabase;
}

export function getJWTSecret(): string {
  const secret = process.env['JWT_SECRET'];
  if (!secret) {
    throw new Error('Missing JWT secret');
  }

  return secret;
}
