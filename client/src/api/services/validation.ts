import { z } from 'zod';

export function validateRequest<T>(
  schema: z.ZodSchema<T>,
  data: unknown,
  meta: { headers: Record<string, string | string[] | undefined> },
):
  | { success: true; data: T }
  | {
      success: false;
      reason: 'empty' | 'invalid';
      error: string;
      details: any;
    } {
  if (!data || (typeof data === 'object' && Object.keys(data).length === 0)) {
    return {
      success: false,
      reason: 'empty',
      error: 'Empty request body',
      details: {
        received: data,
        headers: meta.headers,
        contentType: meta.headers['content-type'],
        contentLength: meta.headers['content-length'],
      },
    };
  }

  const result = schema.safeParse(data);
  if (result.success) {
    return { success: true, data: result.data };
  }

  const errors = result.error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
  }));

  return {
    success: false,
    reason: 'invalid',
    error: 'Validation failed',
    details: errors,
  };
}
