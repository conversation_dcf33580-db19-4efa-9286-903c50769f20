import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { getJWTSecret } from '../services';

export interface AuthenticatedRequest extends Request {
  user?: any;
}

export const requireAuth = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
) => {
  const cookieToken = req.cookies?.['access_token'];
  const authHeader = req.headers.authorization;
  const headerToken = authHeader?.startsWith('Bearer ')
    ? authHeader.slice(7)
    : null;

  const token = cookieToken || headerToken;

  if (!token) {
    return res.status(401).json({ error: 'Missing access token' });
  }

  try {
    req.user = jwt.verify(token, getJWTSecret());
    return next();
  } catch (err) {
    return res.status(401).json({ error: 'Invalid access token' });
  }
};
