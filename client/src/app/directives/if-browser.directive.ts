import {
  Directive,
  Inject,
  Input,
  PLATFORM_ID,
  TemplateRef,
  ViewContainerRef,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

@Directive({
  selector: '[ngIfBrowser]',
})
export class IfBrowserDirective {
  constructor(
    private templateRef: TemplateRef<unknown>,
    private viewContainer: ViewContainerRef,
    @Inject(PLATFORM_ID) private platformId: Object,
  ) {}

  @Input()
  set ngIfBrowser(condition: boolean) {
    if (isPlatformBrowser(this.platformId) && condition) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
