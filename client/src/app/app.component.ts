import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';
import * as AOS from 'aos';
import { RouterOutlet } from '@angular/router';

@Component({
  selector: 'chm-root',
  templateUrl: './app.component.html',
  imports: [FormsModule, RouterOutlet],
  styleUrl: './app.component.css',
})
export class AppComponent implements OnInit {
  constructor(@Inject(PLATFORM_ID) private platformId: Object) {}

  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      AOS.init({
        duration: 800,
        easing: 'ease-out',
        once: true,
      });
    }
  }
}
