import { definePreset } from '@primeng/themes';
import Aura from '@primeng/themes/aura';

const ChainmaticPreset = definePreset(Aura, {
  semantic: {
    primary: {
      50: '{purple.50}',
      100: '{purple.100}',
      200: '{purple.200}',
      300: '{purple.300}',
      400: '{purple.400}',
      500: '{purple.500}',
      600: '{purple.600}',
      700: '{purple.700}',
      800: '{purple.800}',
      900: '{purple.900}',
      950: '{purple.950}',
    },
    colorScheme: {
      light: {
        primary: {
          color: '{purple.600}',
          inverseColor: '#ffffff',
          hoverColor: '{purple.700}',
          activeColor: '{purple.800}',
        },
        highlight: {
          background: '{purple.50}',
          focusBackground: '{purple.100}',
          color: '{purple.800}',
          focusColor: '{purple.900}',
        },
      },
      dark: {
        primary: {
          color: '{purple.400}',
          inverseColor: '#ffffff',
          hoverColor: '{purple.300}',
          activeColor: '{purple.200}',
        },
        highlight: {
          background: 'color-mix(in srgb, {purple.400}, transparent 84%)',
          focusBackground: 'color-mix(in srgb, {purple.400}, transparent 76%)',
          color: 'rgba(255,255,255,.87)',
          focusColor: 'rgba(255,255,255,.87)',
        },
      },
    },
    formField: {
      paddingX: '0.5rem',
      paddingY: '0.375rem',
    },
    button: {
      paddingX: '0.75rem',
      paddingY: '0.5rem',
    },
  },
});

export default ChainmaticPreset;
