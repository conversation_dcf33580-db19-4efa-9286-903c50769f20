import { Component } from '@angular/core';
import { ButtonDirective } from 'primeng/button';
import { InputText } from 'primeng/inputtext';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { UserService } from '../../services';

@Component({
  selector: 'chm-sign-in',
  imports: [InputText, ButtonDirective, ReactiveFormsModule],
  templateUrl: './sign-in.component.html',
  styleUrl: './sign-in.component.css',
  standalone: true,
})
export class SignInComponent {
  signInFormGroup!: FormGroup;
  loading = false;
  error: string | null = null;

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
  ) {
    this.signInFormGroup = this.fb.group({
      email: [null, [Validators.required, Validators.email]],
      password: [null, Validators.required],
    });
  }

  async handleSignIn() {
    if (this.signInFormGroup.invalid) return;

    this.userService.signIn(this.signInFormGroup.value).subscribe();
  }
}
