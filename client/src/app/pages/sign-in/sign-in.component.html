<div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 via-white to-purple-100 p-4">
  <form [formGroup]="signInFormGroup"
        class="w-full max-w-sm bg-white shadow-lg rounded-xl p-8 space-y-6 border border-purple-100">
    <h1 class="text-2xl font-medium text-center text-purple-700">Sign in to Chainmatic</h1>

    <div class="space-y-2">
      <label class="text-sm text-gray-600" for="email">Email</label>
      <input class="w-full" formControlName="email" id="email" pInputText placeholder="<EMAIL>" type="email"/>
    </div>

    <div class="space-y-2">
      <label class="text-sm text-gray-600" for="password">Password</label>
      <input class="w-full" formControlName="password" id="password" pInputText placeholder="••••••••" type="password"/>
    </div>

    <div class="pt-2">
      <button (click)="handleSignIn()" [disabled]="signInFormGroup.invalid" class="w-full justify-center" pButton
              type="submit">Sign In
      </button>
    </div>

  </form>
</div>
