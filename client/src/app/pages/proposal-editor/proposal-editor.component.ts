import { Component } from '@angular/core';
import { Textarea } from 'primeng/textarea';
import { Button, ButtonDirective } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { Client } from '../../../lib/models';
import { Select } from 'primeng/select';
import { Dialog } from 'primeng/dialog';
import { InputText } from 'primeng/inputtext';

@Component({
  selector: 'chm-proposal-editor',
  imports: [
    Textarea,
    ButtonDirective,
    FormsModule,
    Button,
    Select,
    Dialog,
    InputText,
  ],
  templateUrl: './proposal-editor.component.html',
  styleUrl: './proposal-editor.component.css',
  standalone: true,
})
export class ProposalEditorComponent {
  selectedClient?: Client;
  clients: Client[] = [];
  visible = false;
}
