<div
  class="max-w-3xl mx-auto p-6 pt rounded-lg shadow-lg ">
  <h2 class="text-2xl font-bold">AI Prompt Assistant</h2>
  <div class="mb-2">
    <div class="flex items-center gap-2">
      <p-select
        [(ngModel)]="selectedClient"
        [options]="clients"
        class="w-full"
        inputId="clientDropdown"
        optionLabel="name"
        panelStyleClass="bg-slate-900 text-white"
        placeholder="Choose a client"
      ></p-select>
      <p-button icon="pi pi-user-plus"/>
    </div>
  </div>
  <textarea
    class="w-full text-white placeholder-pink-200 bg-purple-900 border-purple-700"
    pTextarea
    placeholder="Describe the project or goal for the proposal..."
    rows="6"
    style="border-radius: 0.5rem;"
  ></textarea>
  <button
    class="mt-2 bg-gradient-to-r from-purple-500 to-pink-500 border-none"
    label="Generate Proposal"
    pButton
    style="border-radius: 0.5rem; padding: 0.75rem 1.5rem;"
  ></button>
</div>


<p-dialog [(visible)]="createClientDialogVisible" [style]="{ width: '25rem' }" header="Create New Client">
  <span class="p-text-secondary block mb-6">Enter details for the new client.</span>

  <form [formGroup]="clientForm">
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24" for="clientName">Name</label>
      <input class="flex-auto" formControlName="name" id="clientName" pInputText placeholder="Acme Corp"/>
    </div>
    <div class="flex items-center gap-4 mb-4">
      <label class="font-semibold w-24" for="clientEmail">Email</label>
      <input class="flex-auto" formControlName="email" id="clientEmail" pInputText placeholder="<EMAIL>"/>
    </div>
    <div class="flex items-center gap-4 mb-6">
      <label class="font-semibold w-24" for="clientRep">Representative</label>
      <input class="flex-auto" formControlName="representative" id="clientRep" pInputText placeholder="Jane Doe"/>
    </div>

    <div class="flex justify-end gap-2">
      <p-button (click)="createClientDialogVisible = false" label="Cancel" severity="secondary"/>
      <p-button (click)="createClient()" [disabled]="clientForm.invalid" label="Create"/>
    </div>
  </form>
</p-dialog>
