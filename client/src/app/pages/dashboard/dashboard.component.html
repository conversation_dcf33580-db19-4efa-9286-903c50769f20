<div class="card">
  <p-menu [model]="items" class="flex justify-center" styleClass="w-full md:w-60">
    <ng-template #start>
            <span class="inline-flex items-center gap-1 px-2 py-2">
                 <svg
                   class="block mx-auto"
                   fill="none"
                   height="33"
                   viewBox="0 0 31 33"
                   width="31"
                   xmlns="http://www.w3.org/2000/svg"
                 >
                    <path
                      d="M15.1934 0V0V0L0.0391235 5.38288L2.35052 25.3417L15.1934 32.427V32.427V32.427L28.0364 25.3417L30.3478 5.38288L15.1934 0Z"
                      fill="var(--p-primary-color)"
                    />
                    <mask height="33" id="mask0_1_52" maskUnits="userSpaceOnUse" style="mask-type:luminance" width="31" x="0"
                          y="0">
                        <path
                          d="M15.1934 0V0V0L0.0391235 5.38288L2.35052 25.3417L15.1934 32.427V32.427V32.427L28.0364 25.3417L30.3478 5.38288L15.1934 0Z"
                          fill="var(--ground-background)"
                        />
                    </mask>
                    <g mask="url(#mask0_1_52)">
                        <path
                          clip-rule="evenodd"
                          d="M15.1935 0V3.5994V3.58318V20.0075V20.0075V32.427V32.427L28.0364 25.3417L30.3478 5.38288L15.1935 0Z"
                          fill="var(--p-primary-color)"
                          fill-rule="evenodd"
                        />
                    </g>
                    <path
                      d="M19.6399 15.3776L18.1861 15.0547L19.3169 16.6695V21.6755L23.1938 18.4458V12.9554L21.4169 13.6013L19.6399 15.3776Z"
                      fill="var(--ground-background)"
                    />
                    <path
                      d="M10.5936 15.3776L12.0474 15.0547L10.9166 16.6695V21.6755L7.03966 18.4458V12.9554L8.81661 13.6013L10.5936 15.3776Z"
                      fill="var(--ground-background)"
                    />
                    <path
                      clip-rule="evenodd"
                      d="M11.3853 16.9726L12.6739 15.0309L13.4793 15.5163H16.7008L17.5061 15.0309L18.7947 16.9726V24.254L17.8283 25.7103L16.7008 26.843H13.4793L12.3518 25.7103L11.3853 24.254V16.9726Z"
                      fill="var(--ground-background)"
                      fill-rule="evenodd"
                    />
                    <path d="M19.3168 24.7437L21.4168 22.6444V20.5451L19.3168 22.3214V24.7437Z"
                          fill="var(--ground-background)"/>
                    <path d="M10.9166 24.7437L8.81662 22.6444V20.5451L10.9166 22.3214V24.7437Z"
                          fill="var(--ground-background)"/>
                    <path
                      clip-rule="evenodd"
                      d="M13.0167 5.68861L11.7244 8.7568L13.8244 14.8932H14.7936V5.68861H13.0167ZM15.4397 5.68861V14.8932H16.5706L18.5091 8.7568L17.2167 5.68861H15.4397Z"
                      fill="var(--ground-background)"
                      fill-rule="evenodd"
                    />
                    <path
                      d="M13.8244 14.8932L6.87813 12.3094L5.90888 8.27235L11.8859 8.7568L13.9859 14.8932H13.8244Z"
                      fill="var(--ground-background)"
                    />
                    <path
                      d="M16.5706 14.8932L23.5169 12.3094L24.4861 8.27235L18.3476 8.7568L16.4091 14.8932H16.5706Z"
                      fill="var(--ground-background)"
                    />
                    <path
                      d="M18.8321 8.27235L22.2245 7.94938L19.9629 5.68861H17.7013L18.8321 8.27235Z"
                      fill="var(--ground-background)"
                    />
                    <path
                      d="M11.4013 8.27235L8.00893 7.94938L10.2705 5.68861H12.5321L11.4013 8.27235Z"
                      fill="var(--ground-background)"
                    />
                </svg>
                <span class="text-xl font-semibold">PRIME<span class="text-primary">APP</span></span>
            </span>
    </ng-template>
    <ng-template #submenuheader let-item>
      <span class="text-primary font-bold">{{ item.label }}</span>
    </ng-template>
    <ng-template #item let-item>
      <a class="flex items-center p-menu-item-link" pRipple>
        <span [class]="item.icon"></span>
        <span class="ml-2">{{ item.label }}</span>
        <p-badge *ngIf="item.badge" [value]="item.badge" class="ml-auto"/>
        <span *ngIf="item.shortcut"
              class="ml-auto border border-surface rounded bg-emphasis text-muted-color text-xs p-1">{{ item.shortcut }}</span>
      </a>
    </ng-template>
    <ng-template #end>
      <button class="relative overflow-hidden w-full border-0 bg-transparent flex items-start p-2 pl-4 hover:bg-surface-100 dark:hover:bg-surface-800 rounded-none cursor-pointer transition-colors duration-200"
              pRipple>
        <p-avatar class="mr-2" image="https://primefaces.org/cdn/primeng/images/demo/avatar/amyelsner.png"
                  shape="circle"/>
        <span class="inline-flex flex-col">
                    <span class="font-bold">Amy Elsner</span>
                    <span class="text-sm">Admin</span>
                </span>
      </button>
    </ng-template>
  </p-menu>
</div>
