import { HttpClient } from '@angular/common/http';
import { inject, Injectable, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, finalize, Observable, of, tap } from 'rxjs';
import { Router } from '@angular/router';
import { SignInRequest } from '../../lib/validators';
import { User } from '@supabase/supabase-js';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  isLoading = new BehaviorSubject<boolean>(true);
  private platformId = inject(PLATFORM_ID);
  private userSubject = new BehaviorSubject<User | null>(null);
  private refreshTimer: any = null;

  constructor(
    private http: HttpClient,
    private router: Router,
  ) {}

  signIn(model: SignInRequest): Observable<{ user: User }> {
    return this.http.post<{ user: User }>(`/api/users/sign-in`, model).pipe(
      tap((response) => {
        this.userSubject.next(response.user);
        this.startRefreshTimer();
        this.router.navigate(['/dashboard']);
      }),
    );
  }

  getUser(): Observable<User | null> {
    return this.userSubject.asObservable();
  }

  refreshSession(): Observable<{ user: User }> {
    return this.http.post<{ user: User }>(`/api/users/refresh`, {}).pipe(
      tap((response) => {
        this.userSubject.next(response.user);
        this.startRefreshTimer();
      }),
    );
  }

  fetchMe(): Observable<{ user: User }> {
    if (isPlatformBrowser(this.platformId)) {
      const cached = localStorage.getItem('user');
      if (cached) {
        try {
          const user = JSON.parse(cached);
          this.userSubject.next(user);
          this.isLoading.next(false);
          return of({ user });
        } catch (_) {}
      }
    }

    return this.http.get<{ user: User }>('/api/users/me').pipe(
      tap((res) => this.userSubject.next(res.user)),
      finalize(() => this.isLoading.next(false)),
    );
  }

  logout(): void {
    this.userSubject.next(null);
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
    this.router.navigate(['/sign-in']);
  }

  private startRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }
    this.refreshTimer = setTimeout(
      () => {
        this.refreshSession().subscribe();
      },
      1000 * 60 * 50,
    ); // refresh every ~50 minutes
  }
}
