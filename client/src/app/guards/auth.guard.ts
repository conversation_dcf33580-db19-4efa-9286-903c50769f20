import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { UserService } from '../services';
import { catchError, exhaustMap, map, of, take } from 'rxjs';

export const authGuard: CanActivateFn = () => {
  const userService = inject(UserService);
  const router = inject(Router);

  return userService.getUser().pipe(
    take(1),
    exhaustMap((user) => {
      if (user) return of(true);

      return userService.fetchMe().pipe(
        map((result) => {
          return !!result?.user;
        }),
        catchError(() => {
          router.navigate(['/sign-in']);
          return of(false);
        }),
      );
    }),
  );
};
